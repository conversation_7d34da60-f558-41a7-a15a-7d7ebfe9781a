'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import Navbar from '@/components/Navbar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, ShoppingCart, Eye, Star } from 'lucide-react';
import { Template } from '@/types';
import { addOrder } from '@/lib/firestore';
import { toast } from 'sonner';

interface TemplateDetailPageProps {
  template: Template;
}

const TemplateDetailPage: React.FC<TemplateDetailPageProps> = ({ template }) => {
  const [isOrdering, setIsOrdering] = useState(false);

  const handleOrder = async () => {
    setIsOrdering(true);
    try {
      // For demo purposes, we'll use a placeholder email
      // In a real app, you'd get this from the authenticated user
      const userEmail = '<EMAIL>';
      
      await addOrder({
        templateId: template.id,
        userEmail,
        status: 'Requested',
      });
      
      toast.success('Order placed successfully! We will contact you soon.');
    } catch (error) {
      console.error('Error placing order:', error);
      toast.error('Failed to place order. Please try again.');
    } finally {
      setIsOrdering(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <Link href="/" className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-8">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Templates
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Template Image */}
          <div className="space-y-4">
            <div className="relative aspect-video rounded-lg overflow-hidden">
              <Image
                src={template.imageUrl || '/placeholder-template.jpg'}
                alt={template.title}
                fill
                className="object-cover"
                priority
              />
            </div>
            
            {/* Additional Images Placeholder */}
            <div className="grid grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="relative aspect-video rounded-lg overflow-hidden bg-gray-200">
                  <Image
                    src={template.imageUrl || '/placeholder-template.jpg'}
                    alt={`${template.title} view ${i}`}
                    fill
                    className="object-cover opacity-50"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Template Details */}
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
                  {template.category}
                </span>
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                  <span className="text-sm text-gray-600 ml-2">(4.8)</span>
                </div>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{template.title}</h1>
              <p className="text-gray-700 text-lg leading-relaxed">{template.description}</p>
            </div>

            {/* Price and Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-gray-900">
                  ${template.price}
                </CardTitle>
                <CardDescription>
                  One-time purchase • Lifetime updates • Commercial license
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button 
                  className="w-full" 
                  size="lg"
                  onClick={handleOrder}
                  disabled={isOrdering}
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  {isOrdering ? 'Processing...' : 'Order Now'}
                </Button>
                <Button variant="outline" className="w-full" size="lg">
                  <Eye className="h-4 w-4 mr-2" />
                  Live Preview
                </Button>
              </CardContent>
            </Card>

            {/* Features */}
            <Card>
              <CardHeader>
                <CardTitle>What's Included</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Responsive design for all devices
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Clean, modern code
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Easy customization
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    SEO optimized
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Cross-browser compatibility
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Documentation included
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Technical Details */}
            <Card>
              <CardHeader>
                <CardTitle>Technical Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-900">Framework:</span>
                    <p className="text-gray-600">React/Next.js</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-900">Styling:</span>
                    <p className="text-gray-600">Tailwind CSS</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-900">Components:</span>
                    <p className="text-gray-600">shadcn/ui</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-900">License:</span>
                    <p className="text-gray-600">Commercial</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateDetailPage;
