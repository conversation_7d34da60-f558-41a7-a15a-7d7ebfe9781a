import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // For now, we'll handle admin route protection on the client side
  // In a production app, you'd want to verify the JWT token here
  
  const { pathname } = request.nextUrl;
  
  // Allow access to login page
  if (pathname === '/admin/login') {
    return NextResponse.next();
  }
  
  // For admin routes, we'll handle protection in the components
  // This is because we need to check the user's role from Firestore
  return NextResponse.next();
}

export const config = {
  matcher: ['/admin/:path*']
};
