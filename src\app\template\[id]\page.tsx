import React from 'react';
import { notFound } from 'next/navigation';
import TemplateDetailPage from '@/components/TemplateDetailPage';
import { getTemplate } from '@/lib/firestore';

interface TemplatePageProps {
  params: Promise<{ id: string }>;
}

export default async function TemplatePage({ params }: TemplatePageProps) {
  const { id } = await params;
  const template = await getTemplate(id);

  if (!template) {
    notFound();
  }

  return <TemplateDetailPage template={template} />;
}

export async function generateMetadata({ params }: TemplatePageProps) {
  const { id } = await params;
  const template = await getTemplate(id);

  if (!template) {
    return {
      title: 'Template Not Found',
    };
  }

  return {
    title: `${template.title} - KaleidoneX`,
    description: template.description,
  };
}
